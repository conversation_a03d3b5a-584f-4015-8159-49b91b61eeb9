2025-07-30 19:34:48,348 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-30 19:34:48,349 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
INFO:sqlalchemy.engine.Engine:SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 19:34:48,349 INFO sqlalchemy.engine.Engine [cached since 1317s ago] {'pk_1': 14}
INFO:sqlalchemy.engine.Engine:[cached since 1317s ago] {'pk_1': 14}
DEBUG - Loading user editor2 with ID 14
2025-07-30 19:34:48,351 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
INFO:sqlalchemy.engine.Engine:SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:34:48,352 INFO sqlalchemy.engine.Engine [cached since 1317s ago] {'param_1': 14}
INFO:sqlalchemy.engine.Engine:[cached since 1317s ago] {'param_1': 14}
DEBUG - User roles: []
2025-07-30 19:34:48,353 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
INFO:sqlalchemy.engine.Engine:SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:34:48,354 INFO sqlalchemy.engine.Engine [cached since 1317s ago] {'param_1': 14}
INFO:sqlalchemy.engine.Engine:[cached since 1317s ago] {'param_1': 14}
2025-07-30 19:34:48,356 INFO sqlalchemy.engine.Engine SELECT clients.id AS clients_id, clients.name AS clients_name, count(contents.id) AS pending_count
FROM clients INNER JOIN contents ON clients.id = contents.client_id
WHERE contents.is_deleted = false AND contents.image_editor_id = %(image_editor_id_1)s AND (contents.workflow_status = %(workflow_status_1)s OR contents.workflow_status = %(workflow_status_2)s OR contents.internal_review_status LIKE %(internal_review_status_1)s) GROUP BY clients.id, clients.name ORDER BY clients.name
INFO:sqlalchemy.engine.Engine:SELECT clients.id AS clients_id, clients.name AS clients_name, count(contents.id) AS pending_count
FROM clients INNER JOIN contents ON clients.id = contents.client_id
WHERE contents.is_deleted = false AND contents.image_editor_id = %(image_editor_id_1)s AND (contents.workflow_status = %(workflow_status_1)s OR contents.workflow_status = %(workflow_status_2)s OR contents.internal_review_status LIKE %(internal_review_status_1)s) GROUP BY clients.id, clients.name ORDER BY clients.name
2025-07-30 19:34:48,357 INFO sqlalchemy.engine.Engine [cached since 42.89s ago] {'image_editor_id_1': 14, 'workflow_status_1': 'first_reviewed', 'workflow_status_2': 'image_uploaded', 'internal_review_status_1': 'final_rej%'}
INFO:sqlalchemy.engine.Engine:[cached since 42.89s ago] {'image_editor_id_1': 14, 'workflow_status_1': 'first_reviewed', 'workflow_status_2': 'image_uploaded', 'internal_review_status_1': 'final_rej%'}
2025-07-30 19:34:48,360 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
127.0.0.1 - - [30/Jul/2025 19:34:48] "GET /simple/image-upload HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [30/Jul/2025 19:34:48] "GET /simple/image-upload HTTP/1.1" 200 -
2025-07-30 19:34:48,372 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
127.0.0.1 - - [30/Jul/2025 19:34:48] "GET /static/js/bootstrap-simple.min.js HTTP/1.1" 304 -
2025-07-30 19:34:48,373 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
INFO:werkzeug:127.0.0.1 - - [30/Jul/2025 19:34:48] "GET /static/js/bootstrap-simple.min.js HTTP/1.1" 304 -
INFO:sqlalchemy.engine.Engine:SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 19:34:48,377 INFO sqlalchemy.engine.Engine [cached since 1317s ago] {'pk_1': 14}
INFO:sqlalchemy.engine.Engine:[cached since 1317s ago] {'pk_1': 14}
DEBUG - Loading user editor2 with ID 14
2025-07-30 19:34:48,382 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
INFO:sqlalchemy.engine.Engine:SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:34:48,382 INFO sqlalchemy.engine.Engine [cached since 1317s ago] {'param_1': 14}
INFO:sqlalchemy.engine.Engine:[cached since 1317s ago] {'param_1': 14}
DEBUG - User roles: []
2025-07-30 19:34:48,384 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
INFO:sqlalchemy.engine.Engine:SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:34:48,385 INFO sqlalchemy.engine.Engine [cached since 1317s ago] {'param_1': 14}
INFO:sqlalchemy.engine.Engine:[cached since 1317s ago] {'param_1': 14}
2025-07-30 19:34:48,387 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
127.0.0.1 - - [30/Jul/2025 19:34:48] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 -
INFO:werkzeug:127.0.0.1 - - [30/Jul/2025 19:34:48] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 -       
127.0.0.1 - - [30/Jul/2025 19:34:48] "GET /favicon.ico HTTP/1.1" 304 -
INFO:werkzeug:127.0.0.1 - - [30/Jul/2025 19:34:48] "GET /favicon.ico HTTP/1.1" 304 -