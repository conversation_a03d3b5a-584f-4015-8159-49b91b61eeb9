2025-07-30 19:40:33,179 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 19:40:33,179 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 19:40:33,180 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'pk_1': 1}
DEBUG - Loading user admin with ID 1
2025-07-30 19:40:33,181 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:40:33,181 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 1}
DEBUG - User roles: ['超级管理员']
2025-07-30 19:40:33,183 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 19:40:33,183 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 2}
DEBUG - Role 超级管理员 permissions: ['dashboard.view', 'content.final_review', 'publish.manage', 'image.upload', 'template.manage', 'system.settings', 'content.review', 'publish.status', 'client.manage', 'user.manage', 'content.generate', 'client.review']2025-07-30 19:40:33,184 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.is_active = true ORDER BY menu_items.`order`
2025-07-30 19:40:33,184 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {}
2025-07-30 19:40:33,187 INFO sqlalchemy.engine.Engine SELECT clients.id AS clients_id, clients.name AS clients_name, clients.contact AS clients_contact, clients.phone AS clients_phone, clients.email AS clients_email, clients.need_review AS clients_need_review, clients.daily_content_count AS clients_daily_content_count, clients.display_start_time AS clients_display_start_time, clients.interval_min AS clients_interval_min, clients.interval_max AS clients_interval_max, clients.review_timeout_hours AS clients_review_timeout_hours, clients.review_deadline_time AS clients_review_deadline_time, clients.auto_approve_enabled AS clients_auto_approve_enabled, clients.created_at AS clients_created_at, clients.updated_at AS clients_updated_at, clients.status AS clients_status, clients.ext_json AS clients_ext_json, clients.default_required_topics AS clients_default_required_topics, clients.default_random_topics AS clients_default_random_topics, clients.default_at_users AS clients_default_at_users, clients.default_location 
AS clients_default_location
FROM clients
WHERE clients.status = true
2025-07-30 19:40:33,187 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {}
2025-07-30 19:40:33,189 INFO sqlalchemy.engine.Engine SELECT template_categories.id AS template_categories_id, template_categories.name AS template_categories_name, template_categories.parent_id AS template_categories_parent_id, template_categories.sort_order AS template_categories_sort_order, template_categories.created_at AS template_categories_created_at, template_categories.updated_at AS template_categories_updated_at
FROM template_categories
2025-07-30 19:40:33,189 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {}
2025-07-30 19:40:33,190 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.is_active = true
2025-07-30 19:40:33,191 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {}
2025-07-30 19:40:33,193 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:40:33,194 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'param_1': 1}
2025-07-30 19:40:33,196 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:40:33,196 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'param_1': 2}
2025-07-30 19:40:33,198 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:40:33,198 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'param_1': 3}
2025-07-30 19:40:33,200 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:40:33,200 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 4}
2025-07-30 19:40:33,201 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:40:33,201 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 5}
2025-07-30 19:40:33,202 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:40:33,203 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 6}
2025-07-30 19:40:33,204 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:40:33,204 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 7}
2025-07-30 19:40:33,205 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:40:33,205 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 13}
2025-07-30 19:40:33,206 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 19:40:33,206 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 14}
2025-07-30 19:40:33,207 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:40:33,207 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 2}
2025-07-30 19:40:33,207 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:40:33,207 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 7}
2025-07-30 19:40:33,208 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:40:33,208 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 13}
2025-07-30 19:40:33,209 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.is_active = true ORDER BY menu_items.`order`
2025-07-30 19:40:33,209 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {}
2025-07-30 19:40:33,210 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 19:40:33,210 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 3}
2025-07-30 19:40:33,211 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,211 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'content.view'}
2025-07-30 19:40:33,212 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,212 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'content.manage'}
2025-07-30 19:40:33,213 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:40:33,213 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 3}
2025-07-30 19:40:33,214 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 19:40:33,214 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 7}
2025-07-30 19:40:33,215 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,215 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'content.view'}
2025-07-30 19:40:33,216 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,216 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'image.view'}
2025-07-30 19:40:33,217 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,217 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'image.manage'}
2025-07-30 19:40:33,218 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:40:33,218 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 4}
2025-07-30 19:40:33,219 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 19:40:33,219 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 8}
2025-07-30 19:40:33,220 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,220 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'content.view'}
2025-07-30 19:40:33,221 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,221 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'review.final'}
2025-07-30 19:40:33,221 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:40:33,222 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 5}
2025-07-30 19:40:33,222 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 19:40:33,222 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 6}
2025-07-30 19:40:33,223 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,223 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'dashboard.view'}
2025-07-30 19:40:33,224 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:40:33,224 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 6}
2025-07-30 19:40:33,225 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 19:40:33,225 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 5}
2025-07-30 19:40:33,225 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,226 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'publish.view'}
2025-07-30 19:40:33,227 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,227 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'publish.manage'}
2025-07-30 19:40:33,228 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,228 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'notification.view'}
2025-07-30 19:40:33,228 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.permission = %(permission_1)s AND menu_items.is_active = true
2025-07-30 19:40:33,229 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'permission_1': 'notification.manage'}
2025-07-30 19:40:33,229 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 19:40:33,229 INFO sqlalchemy.engine.Engine [cached since 109.2s ago] {'param_1': 14}
2025-07-30 19:40:33,230 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.client_id = %(client_id_1)s AND tasks.name = %(name_1)s
 LIMIT %(param_1)s
2025-07-30 19:40:33,230 INFO sqlalchemy.engine.Engine [cached since 108s ago] {'client_id_1': 1, 'name_1': '2025年07月30日任务', 'param_1': 1}
2025-07-30 19:40:33,231 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1
FROM (SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.task_id = %(task_id_1)s) AS anon_1
2025-07-30 19:40:33,232 INFO sqlalchemy.engine.Engine [cached since 108s ago] {'task_id_1': 14}
2025-07-30 19:40:33,232 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.client_id = %(client_id_1)s
2025-07-30 19:40:33,232 INFO sqlalchemy.engine.Engine [cached since 109.1s ago] {'client_id_1': 1}
=== 表单验证通过，开始处理文案生成 ===
表单数据: ImmutableMultiDict([('csrf_token', 'IjM2OTNiM2NhZjI0ZWUwNjMyYWFiMTQ1OTljNjdlMGYxYWY0MWY4OWYi.aIoEiw.WQmXbewxFnoj1jeogoFfEdsz3cg'), ('form_validated', '1'), ('client_id', '1'), ('task_id', '0'), ('new_task_name', '2025年07月30日任务'), ('batch_name', '批次 2'), ('template_category_id', '1'), ('required_topics', 'ee \r\nrr \r\ntt \r\nyy \r\njj \r\nll \r\nbb '), ('random_topics', 'ee \r\nrr \r\ntt \r\nyy \r\njj \r\nll \r\nbb '), ('at_users', '@ee \r\n@rr \r\n@tt \r\n@yy \r\n@jj \r\n@ll \r\n@bb '), ('location', 'ee rr tt yy jj ll bb '), ('content_editor_id', '2'), ('image_editor_id', '14'), ('max_topics_count', '10'), ('random_at_users_count', '1'), ('publish_priority', 'normal'), ('count', '1'), ('duplicate_control', 'task'), ('keywords', '品牌名称: rr, tt, yy, jj\r\n商品名称: rr, tt, yy, jj\r\n店铺地址: rr, tt, yy, jj'), ('allow_template_duplicate', '0')])
表单错误: {}
DEBUG - 获取到的表单数据:
  client_id: 1
  task_id: 0
  template_category_id: 1
  count: 1
🔥🔥🔥 EARLY DEBUG - 编辑人员分配: image_editor_id=3, content_editor_id=2
ERROR:root:🔥🔥🔥 EARLY DEBUG - 编辑人员分配: image_editor_id=3, content_editor_id=2
DEBUG - 新任务名称: 2025年07月30日任务
2025-07-30 19:40:33,236 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.client_id = %(client_id_1)s AND tasks.name = %(name_1)s
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT tasks.id AS tasks_id, tasks.client_id AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.client_id = %(client_id_1)s AND tasks.name = %(name_1)s
 LIMIT %(param_1)s
2025-07-30 19:40:33,237 INFO sqlalchemy.engine.Engine [cached since 108s ago] {'client_id_1': 1, 'name_1': '2025年07月30日任务', 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 108s ago] {'client_id_1': 1, 'name_1': '2025年07月30日任务', 'param_1': 1}
使用现有任务: 2025年07月30日任务 (ID: 14)
2025-07-30 19:40:33,238 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1
FROM (SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.task_id = %(task_id_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1
FROM (SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.task_id = %(task_id_1)s) AS anon_1
2025-07-30 19:40:33,239 INFO sqlalchemy.engine.Engine [cached since 108s ago] {'task_id_1': 14}
INFO:sqlalchemy.engine.Engine:[cached since 108s ago] {'task_id_1': 14}
2025-07-30 19:40:33,241 INFO sqlalchemy.engine.Engine INSERT INTO batches (task_id, name, content_count, created_at, created_by) VALUES (%(task_id)s, %(name)s, %(content_count)s, %(created_at)s, %(created_by)s)
INFO:sqlalchemy.engine.Engine:INSERT INTO batches (task_id, name, content_count, created_at, created_by) VALUES (%(task_id)s, %(name)s, %(content_count)s, %(created_at)s, %(created_by)s)
2025-07-30 19:40:33,242 INFO sqlalchemy.engine.Engine [generated in 0.00041s] {'task_id': 14, 'name': '批次2', 'content_count': 
0, 'created_at': datetime.datetime(2025, 7, 30, 19, 40, 33, 239740), 'created_by': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00041s] {'task_id': 14, 'name': '批次2', 'content_count': 0, 'created_at': datetime.datetime(2025, 7, 30, 19, 40, 33, 239740), 'created_by': 1}
创建批次: 批次2 (ID: 29) for 任务ID: 14
DEBUG - 接收到的关键词数据: 品牌名称: rr, tt, yy, jj
商品名称: rr, tt, yy, jj
店铺地址: rr, tt, yy, jj
DEBUG - 解析标记 品牌名称: ['rr', 'tt', 'yy', 'jj']
DEBUG - 解析标记 商品名称: ['rr', 'tt', 'yy', 'jj']
DEBUG - 解析标记 店铺地址: ['rr', 'tt', 'yy', 'jj']
DEBUG - 最终解析的关键词字典: {'品牌名称': ['rr', 'tt', 'yy', 'jj'], '商品名称': ['rr', 'tt', 'yy', 'jj'], '店铺地址': ['rr', 'tt', 'yy', 'jj']}
DEBUG - 处理后的数据:
  required_topics: ee
rr
tt
yy
jj
ll
bb
  random_topics: ee
rr
tt
yy
jj
ll
bb
  at_users: @ee
@rr
@tt
@yy
@jj
@ll
@bb
  location: ee rr tt yy jj ll bb
DEBUG - 编辑人员分配: image_editor_id=3, content_editor_id=2
ERROR:root:🔥 编辑人员分配: image_editor_id=3, content_editor_id=2
DEBUG - 图片编辑管理员: editor(ID:3)
ERROR:root:🔥 图片编辑管理员: editor(ID:3)
DEBUG - 文案编辑管理员: reviewer(ID:2)
ERROR:root:🔥 文案编辑管理员: reviewer(ID:2)
DEBUG - 开始调用generate_contents函数
2025-07-30 19:40:33,246 INFO sqlalchemy.engine.Engine SELECT templates.id AS templates_id, templates.category_id AS templates_category_id, templates.title AS templates_title, templates.content AS templates_content, templates.marks AS templates_marks, templates.creator_id AS templates_creator_id, templates.created_at AS templates_created_at, templates.updated_at AS templates_updated_at, templates.status AS templates_status
FROM templates
WHERE templates.category_id = %(category_id_1)s AND templates.status = true
INFO:sqlalchemy.engine.Engine:SELECT templates.id AS templates_id, templates.category_id AS templates_category_id, templates.title AS templates_title, templates.content AS templates_content, templates.marks AS templates_marks, templates.creator_id AS templates_creator_id, templates.created_at AS templates_created_at, templates.updated_at AS templates_updated_at, templates.status AS templates_status
FROM templates
WHERE templates.category_id = %(category_id_1)s AND templates.status = true
2025-07-30 19:40:33,247 INFO sqlalchemy.engine.Engine [cached since 107.9s ago] {'category_id_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 107.9s ago] {'category_id_1': 1}
2025-07-30 19:40:33,249 INFO sqlalchemy.engine.Engine SELECT DISTINCT contents.template_id AS contents_template_id
FROM contents INNER JOIN templates ON contents.template_id = templates.id
WHERE contents.task_id = %(task_id_1)s AND templates.category_id = %(category_id_1)s AND contents.is_deleted != true
INFO:sqlalchemy.engine.Engine:SELECT DISTINCT contents.template_id AS contents_template_id
FROM contents INNER JOIN templates ON contents.template_id = templates.id
WHERE contents.task_id = %(task_id_1)s AND templates.category_id = %(category_id_1)s AND contents.is_deleted != true
2025-07-30 19:40:33,249 INFO sqlalchemy.engine.Engine [generated in 0.00054s] {'task_id_1': 14, 'category_id_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00054s] {'task_id_1': 14, 'category_id_1': 1}
位置信息调试:
原始location: 'ee rr tt yy jj ll bb'
处理后location_list: ['ee', 'rr', 'tt', 'yy', 'jj', 'll', 'bb']
2025-07-30 19:40:33,251 INFO sqlalchemy.engine.Engine SELECT clients.id AS clients_id, clients.name AS clients_name, clients.contact AS clients_contact, clients.phone AS clients_phone, clients.email AS clients_email, clients.need_review AS clients_need_review, clients.daily_content_count AS clients_daily_content_count, clients.display_start_time AS clients_display_start_time, clients.interval_min AS clients_interval_min, clients.interval_max AS clients_interval_max, clients.review_timeout_hours AS clients_review_timeout_hours, clients.review_deadline_time AS clients_review_deadline_time, clients.auto_approve_enabled AS clients_auto_approve_enabled, clients.created_at AS clients_created_at, clients.updated_at AS clients_updated_at, clients.status AS clients_status, clients.ext_json AS clients_ext_json, clients.default_required_topics AS clients_default_required_topics, clients.default_random_topics AS clients_default_random_topics, clients.default_at_users AS clients_default_at_users, clients.default_location 
AS clients_default_location
FROM clients
WHERE clients.id = %(pk_1)s
INFO:sqlalchemy.engine.Engine:SELECT clients.id AS clients_id, clients.name AS clients_name, clients.contact AS clients_contact, clients.phone AS clients_phone, clients.email AS clients_email, clients.need_review AS clients_need_review, clients.daily_content_count AS clients_daily_content_count, clients.display_start_time AS clients_display_start_time, clients.interval_min AS clients_interval_min, clients.interval_max AS clients_interval_max, clients.review_timeout_hours AS clients_review_timeout_hours, clients.review_deadline_time AS clients_review_deadline_time, clients.auto_approve_enabled AS clients_auto_approve_enabled, clients.created_at AS clients_created_at, clients.updated_at AS clients_updated_at, clients.status AS clients_status, clients.ext_json 
AS clients_ext_json, clients.default_required_topics AS clients_default_required_topics, clients.default_random_topics AS clients_default_random_topics, clients.default_at_users AS clients_default_at_users, clients.default_location AS clients_default_location
FROM clients
WHERE clients.id = %(pk_1)s
2025-07-30 19:40:33,252 INFO sqlalchemy.engine.Engine [cached since 108.1s ago] {'pk_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 108.1s ago] {'pk_1': 1}
process_location_for_single_article 输入: ['ee', 'rr', 'tt', 'yy', 'jj', 'll', 'bb']
随机选择的位置: 'yy'
内容长度符合要求 - 标题: 18/20, 内容: 92/1000, 状态设置为: draft, 内部状态: pending
WARNING:root:DEBUG: 创建文案对象 - image_editor_id=3, content_editor_id=2
WARNING:root:DEBUG: 文案对象创建完成 - content_obj.image_editor_id=3, content_obj.content_editor_id=2
2025-07-30 19:40:33,287 INFO sqlalchemy.engine.Engine INSERT INTO contents (client_id, task_id, batch_id, template_id, title, content, topics, location, image_urls, display_date, display_time, workflow_status, publish_status, client_review_status, internal_review_status, publish_priority, publish_time, publish_error, publish_retry_count, status_update_time, created_at, updated_at, 
created_by, reviewer_id, review_time, image_editor_id, content_editor_id, is_deleted, deleted_at, deleted_by, ext_json, content_completed, image_completed) VALUES (%(client_id)s, %(task_id)s, %(batch_id)s, %(template_id)s, %(title)s, %(content)s, %(topics)s, %(location)s, %(image_urls)s, %(display_date)s, %(display_time)s, %(workflow_status)s, %(publish_status)s, %(client_review_status)s, %(internal_review_status)s, %(publish_priority)s, %(publish_time)s, %(publish_error)s, %(publish_retry_count)s, %(status_update_time)s, %(created_at)s, %(updated_at)s, %(created_by)s, %(reviewer_id)s, %(review_time)s, %(image_editor_id)s, %(content_editor_id)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by)s, %(ext_json)s, %(content_completed)s, %(image_completed)s)
INFO:sqlalchemy.engine.Engine:INSERT INTO contents (client_id, task_id, batch_id, template_id, title, content, topics, location, image_urls, display_date, display_time, workflow_status, publish_status, client_review_status, internal_review_status, publish_priority, publish_time, publish_error, publish_retry_count, status_update_time, created_at, updated_at, created_by, reviewer_id, review_time, image_editor_id, content_editor_id, is_deleted, deleted_at, deleted_by, ext_json, content_completed, image_completed) VALUES (%(client_id)s, %(task_id)s, %(batch_id)s, %(template_id)s, %(title)s, %(content)s, %(topics)s, %(location)s, %(image_urls)s, %(display_date)s, %(display_time)s, %(workflow_status)s, %(publish_status)s, %(client_review_status)s, %(internal_review_status)s, %(publish_priority)s, %(publish_time)s, %(publish_error)s, %(publish_retry_count)s, %(status_update_time)s, %(created_at)s, %(updated_at)s, %(created_by)s, %(reviewer_id)s, %(review_time)s, %(image_editor_id)s, %(content_editor_id)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by)s, %(ext_json)s, %(content_completed)s, %(image_completed)s)
2025-07-30 19:40:33,288 INFO sqlalchemy.engine.Engine [generated in 0.00084s] {'client_id': 1, 'task_id': 14, 'batch_id': 29, 'template_id': 96, 'title': '早餐新选择！✅ yyjj开启元气一天☀️', 'content': '✅ jj\r\n早起就是为了这口jj！现点现做，酥脆爆浆，搭配咖
啡绝绝子～打工人早餐首选 #早餐推荐 #美食日记', 'topics': '["ee", "rr", "tt", "yy", "jj", "ll", "bb", "ee", "tt", "ll"]', 'location': 'yy', 'image_urls': None, 'display_date': datetime.date(2025, 7, 30), 'display_time': datetime.time(8, 30), 'workflow_status': 'draft', 'publish_status': 'unpublished', 'client_review_status': 'pending', 'internal_review_status': 'pending', 'publish_priority': 'normal', 'publish_time': None, 'publish_error': None, 'publish_retry_count': 0, 'status_update_time': datetime.datetime(2025, 7, 30, 19, 40, 33, 287744), 'created_at': datetime.datetime(2025, 7, 30, 19, 40, 33, 287744), 'updated_at': datetime.datetime(2025, 7, 30, 19, 40, 33, 287744), 'created_by': 1, 'reviewer_id': None, 'review_time': None, 'image_editor_id': 3, 'content_editor_id': 2, 'is_deleted': 0, 'deleted_at': None, 'deleted_by': None, 'ext_json': '{"at_users": ["@bb"]}', 'content_completed': 1, 'image_completed': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00084s] {'client_id': 1, 'task_id': 14, 'batch_id': 29, 'template_id': 96, 'title': '早餐新选择！✅ yyjj开启元气一天☀️', 'content': '✅ jj\r\n早起就是为了这口jj！现点现做，酥脆爆浆，搭配咖啡绝绝子～打工人早餐首选
 #早餐推荐 #美食日记', 'topics': '["ee", "rr", "tt", "yy", "jj", "ll", "bb", "ee", "tt", "ll"]', 'location': 'yy', 'image_urls': None, 'display_date': datetime.date(2025, 7, 30), 'display_time': datetime.time(8, 30), 'workflow_status': 'draft', 'publish_status': 'unpublished', 'client_review_status': 'pending', 'internal_review_status': 'pending', 'publish_priority': 'normal', 'publish_time': None, 'publish_error': None, 'publish_retry_count': 0, 'status_update_time': datetime.datetime(2025, 7, 30, 19, 40, 
33, 287744), 'created_at': datetime.datetime(2025, 7, 30, 19, 40, 33, 287744), 'updated_at': datetime.datetime(2025, 7, 30, 19, 
40, 33, 287744), 'created_by': 1, 'reviewer_id': None, 'review_time': None, 'image_editor_id': 3, 'content_editor_id': 2, 'is_deleted': 0, 'deleted_at': None, 'deleted_by': None, 'ext_json': '{"at_users": ["@bb"]}', 'content_completed': 1, 'image_completed': 1}
2025-07-30 19:40:33,290 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-30 19:40:33,297 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-30 19:40:33,299 INFO sqlalchemy.engine.Engine SELECT contents.id AS contents_id, contents.client_id AS contents_client_id, contents.task_id AS contents_task_id, contents.batch_id AS contents_batch_id, contents.template_id AS contents_template_id, contents.title AS contents_title, contents.content AS contents_content, contents.topics AS contents_topics, contents.location AS 
contents_location, contents.image_urls AS contents_image_urls, contents.display_date AS contents_display_date, contents.display_time AS contents_display_time, contents.workflow_status AS contents_workflow_status, contents.publish_status AS contents_publish_status, contents.client_review_status AS contents_client_review_status, contents.internal_review_status AS contents_internal_review_status, contents.publish_priority AS contents_publish_priority, contents.publish_time AS contents_publish_time, contents.publish_error AS contents_publish_error, contents.publish_retry_count AS contents_publish_retry_count, contents.status_update_time AS contents_status_update_time, contents.created_at AS contents_created_at, contents.updated_at AS contents_updated_at, contents.created_by AS contents_created_by, contents.reviewer_id AS contents_reviewer_id, contents.review_time AS contents_review_time, contents.image_editor_id AS contents_image_editor_id, contents.content_editor_id AS contents_content_editor_id, contents.is_deleted AS contents_is_deleted, contents.deleted_at AS contents_deleted_at, contents.deleted_by AS contents_deleted_by, contents.ext_json AS contents_ext_json, contents.content_completed AS contents_content_completed, contents.image_completed AS contents_image_completed
FROM contents
WHERE contents.id = %(pk_1)s
INFO:sqlalchemy.engine.Engine:SELECT contents.id AS contents_id, contents.client_id AS contents_client_id, contents.task_id AS contents_task_id, contents.batch_id AS contents_batch_id, contents.template_id AS contents_template_id, contents.title AS contents_title, contents.content AS contents_content, contents.topics AS contents_topics, contents.location AS contents_location, contents.image_urls AS contents_image_urls, contents.display_date AS contents_display_date, contents.display_time AS contents_display_time, contents.workflow_status AS contents_workflow_status, contents.publish_status AS contents_publish_status, contents.client_review_status AS contents_client_review_status, contents.internal_review_status AS contents_internal_review_status, contents.publish_priority AS contents_publish_priority, contents.publish_time AS contents_publish_time, contents.publish_error AS contents_publish_error, contents.publish_retry_count AS contents_publish_retry_count, contents.status_update_time AS contents_status_update_time, contents.created_at AS contents_created_at, contents.updated_at AS contents_updated_at, contents.created_by AS contents_created_by, contents.reviewer_id AS contents_reviewer_id, contents.review_time AS contents_review_time, contents.image_editor_id AS contents_image_editor_id, contents.content_editor_id AS contents_content_editor_id, contents.is_deleted AS contents_is_deleted, contents.deleted_at AS contents_deleted_at, contents.deleted_by AS contents_deleted_by, contents.ext_json AS contents_ext_json, contents.content_completed AS contents_content_completed, contents.image_completed AS contents_image_completed
FROM contents
WHERE contents.id = %(pk_1)s
2025-07-30 19:40:33,301 INFO sqlalchemy.engine.Engine [generated in 0.00248s] {'pk_1': 102}
INFO:sqlalchemy.engine.Engine:[generated in 0.00248s] {'pk_1': 102}
WARNING:root:DEBUG: 文案保存后验证 - ID=102, image_editor_id=3, content_editor_id=2
2025-07-30 19:40:33,307 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1
FROM (SELECT contents.id AS contents_id, contents.client_id AS contents_client_id, contents.task_id AS contents_task_id, contents.batch_id AS contents_batch_id, contents.template_id AS contents_template_id, contents.title AS contents_title, contents.content AS contents_content, contents.topics AS contents_topics, contents.location AS contents_location, contents.image_urls AS contents_image_urls, contents.display_date AS contents_display_date, contents.display_time AS contents_display_time, contents.workflow_status AS contents_workflow_status, contents.publish_status AS contents_publish_status, contents.client_review_status AS contents_client_review_status, contents.internal_review_status AS contents_internal_review_status, contents.publish_priority AS contents_publish_priority, contents.publish_time AS contents_publish_time, contents.publish_error AS contents_publish_error, contents.publish_retry_count AS contents_publish_retry_count, contents.status_update_time AS contents_status_update_time, contents.created_at AS contents_created_at, contents.updated_at AS contents_updated_at, contents.created_by AS contents_created_by, contents.reviewer_id AS contents_reviewer_id, contents.review_time AS contents_review_time, contents.image_editor_id AS contents_image_editor_id, contents.content_editor_id AS contents_content_editor_id, contents.is_deleted AS contents_is_deleted, contents.deleted_at AS contents_deleted_at, contents.deleted_by AS contents_deleted_by, contents.ext_json AS contents_ext_json, contents.content_completed AS contents_content_completed, contents.image_completed AS contents_image_completed
FROM contents
WHERE contents.batch_id = %(batch_id_1)s) AS anon_1
INFO:sqlalchemy.engine.Engine:SELECT count(*) AS count_1
FROM (SELECT contents.id AS contents_id, contents.client_id AS contents_client_id, contents.task_id AS contents_task_id, contents.batch_id AS contents_batch_id, contents.template_id AS contents_template_id, contents.title AS contents_title, contents.content AS contents_content, contents.topics AS contents_topics, contents.location AS contents_location, contents.image_urls AS contents_image_urls, contents.display_date AS contents_display_date, contents.display_time AS contents_display_time, contents.workflow_status AS contents_workflow_status, contents.publish_status AS contents_publish_status, contents.client_review_status AS contents_client_review_status, contents.internal_review_status AS contents_internal_review_status, contents.publish_priority AS contents_publish_priority, contents.publish_time AS contents_publish_time, contents.publish_error AS contents_publish_error, contents.publish_retry_count AS contents_publish_retry_count, contents.status_update_time AS contents_status_update_time, contents.created_at AS contents_created_at, contents.updated_at AS contents_updated_at, contents.created_by AS contents_created_by, contents.reviewer_id AS contents_reviewer_id, contents.review_time AS contents_review_time, contents.image_editor_id AS contents_image_editor_id, contents.content_editor_id AS contents_content_editor_id, contents.is_deleted AS contents_is_deleted, contents.deleted_at AS contents_deleted_at, contents.deleted_by AS contents_deleted_by, contents.ext_json AS contents_ext_json, contents.content_completed AS contents_content_completed, contents.image_completed AS contents_image_completed
FROM contents
WHERE contents.batch_id = %(batch_id_1)s) AS anon_1
2025-07-30 19:40:33,308 INFO sqlalchemy.engine.Engine [generated in 0.00141s] {'batch_id_1': 29}
INFO:sqlalchemy.engine.Engine:[generated in 0.00141s] {'batch_id_1': 29}
2025-07-30 19:40:33,310 INFO sqlalchemy.engine.Engine SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.id = %(pk_1)s
INFO:sqlalchemy.engine.Engine:SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.id = %(pk_1)s
2025-07-30 19:40:33,310 INFO sqlalchemy.engine.Engine [generated in 0.00042s] {'pk_1': 29}
INFO:sqlalchemy.engine.Engine:[generated in 0.00042s] {'pk_1': 29}
2025-07-30 19:40:33,312 INFO sqlalchemy.engine.Engine UPDATE batches SET content_count=%(content_count)s WHERE batches.id = %(batches_id)s
INFO:sqlalchemy.engine.Engine:UPDATE batches SET content_count=%(content_count)s WHERE batches.id = %(batches_id)s
2025-07-30 19:40:33,312 INFO sqlalchemy.engine.Engine [generated in 0.00028s] {'content_count': 1, 'batches_id': 29}
INFO:sqlalchemy.engine.Engine:[generated in 0.00028s] {'content_count': 1, 'batches_id': 29}
2025-07-30 19:40:33,313 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
DEBUG - generate_contents返回结果: 1 篇文案
DEBUG - 数据库事务提交成功
2025-07-30 19:40:33,320 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-30 19:40:33,321 INFO sqlalchemy.engine.Engine SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.id = %(pk_1)s
INFO:sqlalchemy.engine.Engine:SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.id = %(pk_1)s
2025-07-30 19:40:33,321 INFO sqlalchemy.engine.Engine [cached since 0.01079s ago] {'pk_1': 29}
INFO:sqlalchemy.engine.Engine:[cached since 0.01079s ago] {'pk_1': 29}
DEBUG - 返回AJAX响应: {'success': True, 'message': '文案生成成功！共生成了 1 篇文案，可以到初审文案页面查看生成的内容', 'clear_form': True, 'generated_count': 1, 'batch_id': 29, 'task_id': 14}
2025-07-30 19:40:33,322 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
127.0.0.1 - - [30/Jul/2025 19:40:33] "POST /simple/content HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [30/Jul/2025 19:40:33] "POST /simple/content HTTP/1.1" 200 -