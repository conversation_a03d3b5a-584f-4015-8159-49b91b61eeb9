2025-07-30 17:25:43,876 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:25:43,877 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 17:25:43,877 INFO sqlalchemy.engine.Engine [cached since 44.39s ago] {'pk_1': 1}
DEBUG - Loading user admin with ID 1
2025-07-30 17:25:43,878 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:25:43,878 INFO sqlalchemy.engine.Engine [cached since 44.39s ago] {'param_1': 1}
DEBUG - User roles: ['超级管理员']
2025-07-30 17:25:43,879 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 17:25:43,880 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {'param_1': 2}
DEBUG - Role 超级管理员 permissions: ['dashboard.view', 'content.final_review', 'publish.manage', 'image.upload', 'template.manage', 'system.settings', 'content.review', 'publish.status', 'client.manage', 'user.manage', 'content.generate', 'client.review']2025-07-30 17:25:43,881 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.is_active = true ORDER BY menu_items.`order`
2025-07-30 17:25:43,881 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {}
2025-07-30 17:25:43,884 INFO sqlalchemy.engine.Engine SELECT clients.id AS clients_id, clients.name AS clients_name, clients.contact AS clients_contact, clients.phone AS clients_phone, clients.email AS clients_email, clients.need_review AS clients_need_review, clients.daily_content_count AS clients_daily_content_count, clients.display_start_time AS clients_display_start_time, clients.interval_min AS clients_interval_min, clients.interval_max AS clients_interval_max, clients.review_timeout_hours AS clients_review_timeout_hours, clients.review_deadline_time AS clients_review_deadline_time, clients.auto_approve_enabled AS clients_auto_approve_enabled, clients.created_at AS clients_created_at, clients.updated_at AS clients_updated_at, clients.status AS clients_status, clients.ext_json AS clients_ext_json, clients.default_required_topics AS clients_default_required_topics, clients.default_random_topics AS clients_default_random_topics, clients.default_at_users AS clients_default_at_users, clients.default_location 
AS clients_default_location
FROM clients
WHERE clients.status = true
2025-07-30 17:25:43,885 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {}
2025-07-30 17:25:43,886 INFO sqlalchemy.engine.Engine SELECT template_categories.id AS template_categories_id, template_categories.name AS template_categories_name, template_categories.parent_id AS template_categories_parent_id, template_categories.sort_order AS template_categories_sort_order, template_categories.created_at AS template_categories_created_at, template_categories.updated_at AS template_categories_updated_at
FROM template_categories
2025-07-30 17:25:43,886 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {}
2025-07-30 17:25:43,888 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.is_active = true
2025-07-30 17:25:43,888 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {}
2025-07-30 17:25:43,889 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 17:25:43,890 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {'param_1': 1}
2025-07-30 17:25:43,892 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 17:25:43,892 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {'param_1': 2}
2025-07-30 17:25:43,894 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 17:25:43,908 INFO sqlalchemy.engine.Engine [cached since 44.4s ago] {'param_1': 3}
2025-07-30 17:25:43,909 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 17:25:43,909 INFO sqlalchemy.engine.Engine [cached since 44.4s ago] {'param_1': 4}
2025-07-30 17:25:43,910 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 17:25:43,910 INFO sqlalchemy.engine.Engine [cached since 44.4s ago] {'param_1': 5}
2025-07-30 17:25:43,911 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 17:25:43,912 INFO sqlalchemy.engine.Engine [cached since 44.4s ago] {'param_1': 6}
2025-07-30 17:25:43,913 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 17:25:43,913 INFO sqlalchemy.engine.Engine [cached since 44.4s ago] {'param_1': 7}
2025-07-30 17:25:43,915 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id
2025-07-30 17:25:43,917 INFO sqlalchemy.engine.Engine [cached since 44.41s ago] {'param_1': 13}
2025-07-30 17:25:43,919 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:25:43,920 INFO sqlalchemy.engine.Engine [cached since 44.43s ago] {'param_1': 2}
2025-07-30 17:25:43,922 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:25:43,922 INFO sqlalchemy.engine.Engine [cached since 44.43s ago] {'param_1': 7}
2025-07-30 17:25:43,924 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:25:43,924 INFO sqlalchemy.engine.Engine [cached since 44.43s ago] {'param_1': 13}
2025-07-30 17:25:43,925 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id
2025-07-30 17:25:43,926 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {'param_1': 2}
2025-07-30 17:25:43,927 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 17:25:43,927 INFO sqlalchemy.engine.Engine [cached since 44.43s ago] {'param_1': 3}
2025-07-30 17:25:43,928 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:25:43,928 INFO sqlalchemy.engine.Engine [cached since 44.44s ago] {'param_1': 3}
2025-07-30 17:25:43,929 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id
2025-07-30 17:25:43,929 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {'param_1': 3}
2025-07-30 17:25:43,930 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:25:43,931 INFO sqlalchemy.engine.Engine [cached since 44.44s ago] {'param_1': 4}
2025-07-30 17:25:43,931 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id
2025-07-30 17:25:43,932 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {'param_1': 4}
2025-07-30 17:25:43,932 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 17:25:43,933 INFO sqlalchemy.engine.Engine [cached since 44.44s ago] {'param_1': 8}
2025-07-30 17:25:43,933 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:25:43,934 INFO sqlalchemy.engine.Engine [cached since 44.44s ago] {'param_1': 5}
2025-07-30 17:25:43,934 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id
2025-07-30 17:25:43,935 INFO sqlalchemy.engine.Engine [cached since 44.38s ago] {'param_1': 5}
2025-07-30 17:25:43,935 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 17:25:43,935 INFO sqlalchemy.engine.Engine [cached since 44.44s ago] {'param_1': 6}
2025-07-30 17:25:43,936 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:25:43,936 INFO sqlalchemy.engine.Engine [cached since 44.44s ago] {'param_1': 6}
2025-07-30 17:25:43,937 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id
2025-07-30 17:25:43,937 INFO sqlalchemy.engine.Engine [cached since 44.39s ago] {'param_1': 6}
2025-07-30 17:25:43,937 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id
2025-07-30 17:25:43,937 INFO sqlalchemy.engine.Engine [cached since 44.44s ago] {'param_1': 5}
2025-07-30 17:25:43,938 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id
2025-07-30 17:25:43,938 INFO sqlalchemy.engine.Engine [cached since 44.39s ago] {'param_1': 7}
2025-07-30 17:25:43,939 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id
2025-07-30 17:25:43,939 INFO sqlalchemy.engine.Engine [cached since 44.39s ago] {'param_1': 13}
2025-07-30 17:25:43,940 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.id = %(pk_1)s
2025-07-30 17:25:43,940 INFO sqlalchemy.engine.Engine [generated in 0.00026s] {'pk_1': 9}
2025-07-30 17:25:43,941 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.client_id = %(client_id_1)s
2025-07-30 17:25:43,941 INFO sqlalchemy.engine.Engine [cached since 44.37s ago] {'client_id_1': 1}
2025-07-30 17:25:43,942 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1
FROM (SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.task_id = %(task_id_1)s) AS anon_1
2025-07-30 17:25:43,942 INFO sqlalchemy.engine.Engine [cached since 42.99s ago] {'task_id_1': 9}
=== 表单验证通过，开始处理文案生成 ===
表单数据: ImmutableMultiDict([('csrf_token', 'IjM2OTNiM2NhZjI0ZWUwNjMyYWFiMTQ1OTljNjdlMGYxYWY0MWY4OWYi.aInk6w.LxVB-xbTfOBOwMiLNpFwnMjbxU0'), ('form_validated', '1'), ('client_id', '1'), ('task_id', '9'), ('new_task_name', '啊啊啊啊啊啊啊'), ('batch_name', 
'批次 3'), ('template_category_id', '11'), ('required_topics', 'ee \r\nrr \r\ntt \r\nyy \r\njj \r\nll \r\nbb '), ('random_topics', 'ee \r\nrr \r\ntt \r\nyy \r\njj \r\nll \r\nbb '), ('at_users', '@ee \r\n@rr \r\n@tt \r\n@yy \r\n@jj \r\n@ll \r\n@bb '), ('location', 'ee rr tt yy jj ll bb '), ('content_editor_id', '13'), ('image_editor_id', '3'), ('max_topics_count', '10'), ('random_at_users_count', '1'), ('publish_priority', 'normal'), ('count', '2'), ('duplicate_control', 'task'), ('keywords', '品牌名称: 地方
\r\n商品名称: 的\r\n店铺地址: 是'), ('allow_template_duplicate', '0')])
表单错误: {}
DEBUG - 获取到的表单数据:
  client_id: 1
  task_id: 9
  template_category_id: 11
  count: 2
2025-07-30 17:25:43,945 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1
FROM (SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.task_id = %(task_id_1)s) AS anon_1
2025-07-30 17:25:43,945 INFO sqlalchemy.engine.Engine [cached since 43s ago] {'task_id_1': 9}
2025-07-30 17:25:43,948 INFO sqlalchemy.engine.Engine INSERT INTO batches (task_id, name, content_count, created_at, created_by) VALUES (%(task_id)s, %(name)s, %(content_count)s, %(created_at)s, %(created_by)s)
2025-07-30 17:25:43,948 INFO sqlalchemy.engine.Engine [generated in 0.00035s] {'task_id': 9, 'name': '批次3', 'content_count': 0, 'created_at': datetime.datetime(2025, 7, 30, 17, 25, 43, 945903), 'created_by': 1}
创建批次: 批次3 (ID: 17) for 任务ID: 9
DEBUG - 接收到的关键词数据: 品牌名称: 地方
商品名称: 的
店铺地址: 是
DEBUG - 解析标记 品牌名称: ['地方']
DEBUG - 解析标记 商品名称: ['的']
DEBUG - 解析标记 店铺地址: ['是']
DEBUG - 最终解析的关键词字典: {'品牌名称': ['地方'], '商品名称': ['的'], '店铺地址': ['是']}
DEBUG - 处理后的数据:
  required_topics: ee
rr
tt
yy
jj
ll
bb
  random_topics: ee
rr
tt
yy
jj
ll
bb
  at_users: @ee
@rr
@tt
@yy
@jj
@ll
@bb
  location: ee rr tt yy jj ll bb
DEBUG - 编辑人员分配: image_editor_id=3, content_editor_id=13
DEBUG - 开始调用generate_contents函数
2025-07-30 17:25:43,952 INFO sqlalchemy.engine.Engine SELECT templates.id AS templates_id, templates.category_id AS templates_category_id, templates.title AS templates_title, templates.content AS templates_content, templates.marks AS templates_marks, templates.creator_id AS templates_creator_id, templates.created_at AS templates_created_at, templates.updated_at AS templates_updated_at, templates.status AS templates_status
FROM templates
WHERE templates.category_id = %(category_id_1)s AND templates.status = true
2025-07-30 17:25:43,952 INFO sqlalchemy.engine.Engine [cached since 42.91s ago] {'category_id_1': 11}
2025-07-30 17:25:43,954 INFO sqlalchemy.engine.Engine SELECT DISTINCT contents.template_id AS contents_template_id
FROM contents INNER JOIN templates ON contents.template_id = templates.id
WHERE contents.task_id = %(task_id_1)s AND templates.category_id = %(category_id_1)s AND contents.is_deleted != true
2025-07-30 17:25:43,954 INFO sqlalchemy.engine.Engine [generated in 0.00026s] {'task_id_1': 9, 'category_id_1': 11}
位置信息调试:
原始location: 'ee rr tt yy jj ll bb'
处理后location_list: ['ee', 'rr', 'tt', 'yy', 'jj', 'll', 'bb']
2025-07-30 17:25:43,955 INFO sqlalchemy.engine.Engine SELECT clients.id AS clients_id, clients.name AS clients_name, clients.contact AS clients_contact, clients.phone AS clients_phone, clients.email AS clients_email, clients.need_review AS clients_need_review, clients.daily_content_count AS clients_daily_content_count, clients.display_start_time AS clients_display_start_time, clients.interval_min AS clients_interval_min, clients.interval_max AS clients_interval_max, clients.review_timeout_hours AS clients_review_timeout_hours, clients.review_deadline_time AS clients_review_deadline_time, clients.auto_approve_enabled AS clients_auto_approve_enabled, clients.created_at AS clients_created_at, clients.updated_at AS clients_updated_at, clients.status AS clients_status, clients.ext_json AS clients_ext_json, clients.default_required_topics AS clients_default_required_topics, clients.default_random_topics AS clients_default_random_topics, clients.default_at_users AS clients_default_at_users, clients.default_location 
AS clients_default_location
FROM clients
WHERE clients.id = %(pk_1)s
2025-07-30 17:25:43,956 INFO sqlalchemy.engine.Engine [cached since 43.03s ago] {'pk_1': 1}
process_location_for_single_article 输入: ['ee', 'rr', 'tt', 'yy', 'jj', 'll', 'bb']
随机选择的位置: 'yy'
内容长度符合要求 - 标题: 16/20, 内容: 101/1000, 状态设置为: draft, 内部状态: pending
process_location_for_single_article 输入: ['ee', 'rr', 'tt', 'yy', 'jj', 'll', 'bb']
随机选择的位置: 'bb'
内容长度超出限制 - 标题: 22/20, 内容: 92/1000, 状态设置为: draft, 内部状态: length_exceeded
2025-07-30 17:25:43,961 INFO sqlalchemy.engine.Engine INSERT INTO contents (client_id, task_id, batch_id, template_id, title, content, topics, location, image_urls, display_date, display_time, workflow_status, publish_status, client_review_status, internal_review_status, publish_priority, publish_time, publish_error, publish_retry_count, status_update_time, created_at, updated_at, 
created_by, reviewer_id, review_time, image_editor_id, content_editor_id, is_deleted, deleted_at, deleted_by, ext_json, content_completed, image_completed) VALUES (%(client_id)s, %(task_id)s, %(batch_id)s, %(template_id)s, %(title)s, %(content)s, %(topics)s, %(location)s, %(image_urls)s, %(display_date)s, %(display_time)s, %(workflow_status)s, %(publish_status)s, %(client_review_status)s, %(internal_review_status)s, %(publish_priority)s, %(publish_time)s, %(publish_error)s, %(publish_retry_count)s, %(status_update_time)s, %(created_at)s, %(updated_at)s, %(created_by)s, %(reviewer_id)s, %(review_time)s, %(image_editor_id)s, %(content_editor_id)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by)s, %(ext_json)s, %(content_completed)s, %(image_completed)s)
2025-07-30 17:25:43,962 INFO sqlalchemy.engine.Engine [generated in 0.00069s] {'client_id': 1, 'task_id': 9, 'batch_id': 17, 'template_id': 93, 'title': '地方必点单品！✅ 的一口就爱上✅ ', 'content': '✅ 是✅ \r\n今天终于吃到地方的招牌的，口感绝了！外脆里嫩， 
酱汁是灵魂～吃完立刻安利给闺蜜? #美食打卡 #吃货日常', 'topics': '["ee", "rr", "tt", "yy", "jj", "ll", "bb", "ee", "jj", "rr"]', 
'location': 'yy', 'image_urls': None, 'display_date': datetime.date(2025, 7, 30), 'display_time': datetime.time(8, 30), 'workflow_status': 'draft', 'publish_status': 'unpublished', 'client_review_status': 'pending', 'internal_review_status': 'pending', 'publish_priority': 'normal', 'publish_time': None, 'publish_error': None, 'publish_retry_count': 0, 'status_update_time': datetime.datetime(2025, 7, 30, 17, 25, 43, 961904), 'created_at': datetime.datetime(2025, 7, 30, 17, 25, 43, 961904), 'updated_at': datetime.datetime(2025, 7, 30, 17, 25, 43, 961904), 'created_by': 1, 'reviewer_id': None, 'review_time': None, 'image_editor_id': 3, 'content_editor_id': 13, 'is_deleted': 0, 'deleted_at': None, 'deleted_by': None, 'ext_json': '{"at_users": ["@rr"]}', 'content_completed': 1, 'image_completed': 1}
2025-07-30 17:25:43,964 INFO sqlalchemy.engine.Engine INSERT INTO contents (client_id, task_id, batch_id, template_id, title, content, topics, location, image_urls, display_date, display_time, workflow_status, publish_status, client_review_status, internal_review_status, publish_priority, publish_time, publish_error, publish_retry_count, status_update_time, created_at, updated_at, 
created_by, reviewer_id, review_time, image_editor_id, content_editor_id, is_deleted, deleted_at, deleted_by, ext_json, content_completed, image_completed) VALUES (%(client_id)s, %(task_id)s, %(batch_id)s, %(template_id)s, %(title)s, %(content)s, %(topics)s, %(location)s, %(image_urls)s, %(display_date)s, %(display_time)s, %(workflow_status)s, %(publish_status)s, %(client_review_status)s, %(internal_review_status)s, %(publish_priority)s, %(publish_time)s, %(publish_error)s, %(publish_retry_count)s, %(status_update_time)s, %(created_at)s, %(updated_at)s, %(created_by)s, %(reviewer_id)s, %(review_time)s, %(image_editor_id)s, %(content_editor_id)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by)s, %(ext_json)s, %(content_completed)s, %(image_completed)s)
2025-07-30 17:25:43,964 INFO sqlalchemy.engine.Engine [cached since 0.003603s ago] {'client_id': 1, 'task_id': 9, 'batch_id': 17, 'template_id': 98, 'title': '✅ 闺蜜下午茶首选?✅ 地方的颜值味道双在线！', 'content': '✅  是✅ \r\n和姐妹约会的秘密基地！的不仅拍
照好看，甜度也刚刚好～搭配花茶完美 #高颜值甜品 #闺蜜约会', 'topics': '["ee", "rr", "tt", "yy", "jj", "ll", "bb", "bb", "yy", "rr"]', 'location': 'bb', 'image_urls': None, 'display_date': datetime.date(2025, 7, 30), 'display_time': datetime.time(8, 40), 'workflow_status': 'draft', 'publish_status': 'unpublished', 'client_review_status': 'pending', 'internal_review_status': 'length_exceeded', 'publish_priority': 'normal', 'publish_time': None, 'publish_error': None, 'publish_retry_count': 0, 'status_update_time': datetime.datetime(2025, 7, 30, 17, 25, 43, 964904), 'created_at': datetime.datetime(2025, 7, 30, 17, 25, 43, 964904), 'updated_at': datetime.datetime(2025, 7, 30, 17, 25, 43, 964904), 'created_by': 1, 'reviewer_id': None, 'review_time': None, 'image_editor_id': 3, 'content_editor_id': 13, 'is_deleted': 0, 'deleted_at': None, 'deleted_by': None, 'ext_json': '{"at_users": ["@yy"]}', 'content_completed': 1, 'image_completed': 1}
2025-07-30 17:25:43,966 INFO sqlalchemy.engine.Engine COMMIT
2025-07-30 17:25:43,977 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:25:44,012 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1 
FROM (SELECT contents.id AS contents_id, contents.client_id AS contents_client_id, contents.task_id AS contents_task_id, contents.batch_id AS contents_batch_id, contents.template_id AS contents_template_id, contents.title AS contents_title, contents.content AS contents_content, contents.topics AS contents_topics, contents.location AS contents_location, contents.image_urls AS contents_image_urls, contents.display_date AS contents_display_date, contents.display_time AS contents_display_time, contents.workflow_status AS contents_workflow_status, contents.publish_status AS contents_publish_status, contents.client_review_status AS contents_client_review_status, contents.internal_review_status AS contents_internal_review_status, contents.publish_priority AS contents_publish_priority, contents.publish_time AS contents_publish_time, contents.publish_error AS contents_publish_error, contents.publish_retry_count AS contents_publish_retry_count, contents.status_update_time AS contents_status_update_time, contents.created_at AS contents_created_at, contents.updated_at AS contents_updated_at, contents.created_by AS contents_created_by, contents.reviewer_id AS contents_reviewer_id, contents.review_time AS contents_review_time, contents.image_editor_id AS contents_image_editor_id, contents.content_editor_id AS contents_content_editor_id, contents.is_deleted AS contents_is_deleted, contents.deleted_at AS contents_deleted_at, contents.deleted_by AS contents_deleted_by, contents.ext_json AS contents_ext_json, contents.content_completed AS contents_content_completed, contents.image_completed AS contents_image_completed
FROM contents
WHERE contents.batch_id = %(batch_id_1)s) AS anon_1
2025-07-30 17:25:44,013 INFO sqlalchemy.engine.Engine [generated in 0.00073s] {'batch_id_1': 17}
2025-07-30 17:25:44,014 INFO sqlalchemy.engine.Engine SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.id = %(pk_1)s
2025-07-30 17:25:44,014 INFO sqlalchemy.engine.Engine [generated in 0.00035s] {'pk_1': 17}
2025-07-30 17:25:44,016 INFO sqlalchemy.engine.Engine UPDATE batches SET content_count=%(content_count)s WHERE batches.id = %(batches_id)s
2025-07-30 17:25:44,016 INFO sqlalchemy.engine.Engine [generated in 0.00022s] {'content_count': 2, 'batches_id': 17}
2025-07-30 17:25:44,017 INFO sqlalchemy.engine.Engine COMMIT
DEBUG - generate_contents返回结果: 2 篇文案
DEBUG - 数据库事务提交成功
2025-07-30 17:25:44,024 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:25:44,024 INFO sqlalchemy.engine.Engine SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.id = %(pk_1)s
2025-07-30 17:25:44,025 INFO sqlalchemy.engine.Engine [cached since 0.01033s ago] {'pk_1': 17}
DEBUG - 返回AJAX响应: {'success': True, 'message': '文案生成成功！共生成了 2 篇文案，可以到初审文案页面查看生成的内容', 'clear_form': True, 'generated_count': 2, 'batch_id': 17, 'task_id': 9}
2025-07-30 17:25:44,025 INFO sqlalchemy.engine.Engine ROLLBACK
127.0.0.1 - - [30/Jul/2025 17:25:44] "POST /simple/content HTTP/1.1" 200 -