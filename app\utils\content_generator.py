import random
import re
import json
from datetime import datetime
from flask import current_app
from sqlalchemy import func
from app.models import db, Template, Content, Batch, Task

def generate_contents(client_id, task_id, batch_id, template_category_id, count,
                     keywords, required_topics, random_topics, max_topics_count,
                     at_users, random_at_users_count, location, publish_priority, avoid_duplicates,
                     allow_template_duplicate, current_user, image_editor_id=None, content_editor_id=None):
    """
    生成文案内容
    
    参数:
    - client_id: 客户ID
    - task_id: 任务ID
    - batch_id: 批次ID
    - template_category_id: 模板分类ID
    - count: 生成数量
    - keywords: 关键词字典 {标记名: [关键词列表]}
    - required_topics: 必选话题字符串
    - random_topics: 随机话题字符串
    - max_topics_count: 最大话题数量（必选话题 + 随机话题 = 总数量）
    - at_users: @用户字符串
    - random_at_users_count: 随机@用户数量
    - location: 定位信息字符串
    - publish_priority: 发布优先级
    - avoid_duplicates: 是否避免重复内容
    - allow_template_duplicate: 是否允许模板重复 (0:任务内不重复, 1:允许重复, 2:批次内不重复)
    - current_user: 当前用户
    
    返回:
    - 生成的文案列表
    """
    try:
        # 获取可用模板
        available_templates = get_available_templates(
            template_category_id, task_id, batch_id, 
            int(allow_template_duplicate)
        )
        
        if not available_templates:
            current_app.logger.warning(f"没有找到可用的模板，分类ID: {template_category_id}")
            return []
        
        # 如果不允许重复且可用模板数量小于请求数量，调整数量
        if int(allow_template_duplicate) == 0 and len(available_templates) < count:
            current_app.logger.warning(
                f"可用模板数量({len(available_templates)})小于请求数量({count})，"
                f"调整生成数量为{len(available_templates)}"
            )
            count = len(available_templates)
        
        # 随机选择模板
        selected_templates = random.sample(available_templates, min(count, len(available_templates)))
        
        # 如果允许重复且需要更多模板
        if int(allow_template_duplicate) > 0 and len(selected_templates) < count:
            # 重复选择模板直到达到请求数量
            additional_count = count - len(selected_templates)
            additional_templates = []
            
            for _ in range(additional_count):
                additional_templates.append(random.choice(available_templates))
            
            selected_templates.extend(additional_templates)
        
        # 处理关键词参数
        if isinstance(keywords, dict):
            # 如果已经是字典格式，直接使用
            keywords_dict = keywords
        else:
            # 如果是字符串格式，解析为字典
            keywords_dict = parse_keywords(keywords)

        # 预处理数据（为每篇文章独立随机做准备）
        required_topics_list = required_topics.strip().split('\n') if required_topics else []
        required_topics_list = [t.strip() for t in required_topics_list if t.strip()]

        random_topics_list = random_topics.strip().split('\n') if random_topics else []
        random_topics_list = [t.strip() for t in random_topics_list if t.strip()]

        at_users_list = at_users.strip().split('\n') if at_users else []
        at_users_list = [u.strip() for u in at_users_list if u.strip()]

        # 处理位置信息，支持换行符或空格分隔
        if location:
            location = location.strip()
            # 优先按换行符分割
            if '\n' in location:
                location_list = location.split('\n')
            # 如果没有换行符，按空格分割
            elif ' ' in location:
                location_list = location.split()
            # 单个位置
            else:
                location_list = [location]
            # 清理空白项
            location_list = [l.strip() for l in location_list if l.strip()]
        else:
            location_list = []

        print(f"位置信息调试:")
        print(f"原始location: {repr(location)}")
        print(f"处理后location_list: {location_list}")

        # 获取客户的每日展示数量设置
        from app.models.client import Client
        client = Client.query.get(client_id)
        daily_count = client.daily_content_count if client else 5  # 默认每天5篇

        # 计算发布日期分配
        from datetime import date, timedelta
        start_date = date.today()

        # 生成文案
        contents = []
        for i, template in enumerate(selected_templates):
            # 计算这篇文案的发布日期
            days_offset = i // daily_count  # 第几天
            display_date = start_date + timedelta(days=days_offset)

            # 计算这篇文案在当天的顺序（用于后续时间分配）
            daily_order = i % daily_count

            # 为每篇文章独立处理话题、@用户和位置
            article_topics = process_topics_for_single_article(
                required_topics_list, random_topics_list, max_topics_count
            )
            article_at_users = process_at_users_for_single_article(at_users_list, random_at_users_count)
            article_location = process_location_for_single_article(location_list)

            content = generate_single_content(
                client_id, task_id, batch_id, template,
                keywords_dict, article_topics, article_at_users,
                article_location, publish_priority, current_user,
                display_date, daily_order, client, image_editor_id, content_editor_id
            )
            contents.append(content)
        
        # 如果需要避免重复内容，进行检查
        if avoid_duplicates:
            contents = remove_duplicate_contents(contents)
        
        # 保存到数据库
        db.session.add_all(contents)
        db.session.commit()
        
        # 更新批次的文案数量
        update_batch_content_count(batch_id)
        
        return contents
    
    except Exception as e:
        current_app.logger.error(f"生成文案出错: {str(e)}")
        db.session.rollback()
        raise e

def get_available_templates(category_id, task_id, batch_id, allow_duplicate):
    """
    获取可用模板
    
    参数:
    - category_id: 模板分类ID
    - task_id: 任务ID
    - batch_id: 批次ID
    - allow_duplicate: 是否允许模板重复 (0:任务内不重复, 1:允许重复, 2:批次内不重复, 3:客户所有任务内不重复)
    
    返回:
    - 可用模板列表
    """
    # 获取分类下的所有模板
    templates = Template.query.filter_by(category_id=category_id, status=True).all()
    
    # 如果允许重复，直接返回所有模板
    if allow_duplicate == 1:
        return templates
    
    # 获取已使用的模板ID
    used_template_ids = []
    
    if allow_duplicate == 0:
        # 任务内不重复：获取该任务下所有已使用的模板ID（限制在当前分类）
        used_template_ids = db.session.query(Content.template_id).join(
            Template, Content.template_id == Template.id
        ).filter(
            Content.task_id == task_id,
            Template.category_id == category_id,
            Content.is_deleted != True
        ).distinct().all()
    elif allow_duplicate == 2:
        # 批次内不重复：获取该批次下所有已使用的模板ID（限制在当前分类）
        used_template_ids = db.session.query(Content.template_id).join(
            Template, Content.template_id == Template.id
        ).filter(
            Content.batch_id == batch_id,
            Template.category_id == category_id,
            Content.is_deleted != True
        ).distinct().all()
    elif allow_duplicate == 3:
        # 获取该任务所属的客户ID
        task = Task.query.get(task_id)
        if task:
            client_id = task.client_id
            # 客户所有任务内不重复：获取该客户所有任务下已使用的模板ID（限制在当前分类）
            used_template_ids = db.session.query(Content.template_id).join(
                Task, Content.task_id == Task.id
            ).join(
                Template, Content.template_id == Template.id
            ).filter(
                Task.client_id == client_id,
                Template.category_id == category_id,
                Content.is_deleted != True
            ).distinct().all()
    
    # 将查询结果转换为ID列表
    used_template_ids = [id[0] for id in used_template_ids]
    
    # 过滤掉已使用的模板
    available_templates = [t for t in templates if t.id not in used_template_ids]
    
    return available_templates

def parse_keywords(keywords_text):
    """解析关键词文本为字典"""
    keywords_dict = {}
    if not keywords_text:
        return keywords_dict
    
    for line in keywords_text.split('\n'):
        if ':' in line:
            mark, values = line.split(':', 1)
            keywords_dict[mark] = values.split(',')
    
    return keywords_dict

def process_topics(required_topics, random_topics, random_topics_count):
    """处理话题，组合必选话题和随机话题"""
    # 处理必选话题
    required_list = required_topics.strip().split('\n') if required_topics else []
    required_list = [t.strip() for t in required_list if t.strip()]
    
    # 处理随机话题
    random_list = random_topics.strip().split('\n') if random_topics else []
    random_list = [t.strip() for t in random_list if t.strip()]
    
    # 如果随机话题数量大于可用随机话题数量，调整数量
    random_topics_count = min(random_topics_count, len(random_list))
    
    # 随机选择指定数量的随机话题
    selected_random_topics = random.sample(random_list, random_topics_count) if random_list and random_topics_count > 0 else []
    
    # 组合所有话题
    all_topics = required_list + selected_random_topics
    
    return all_topics

def process_at_users(at_users_text):
    """处理@用户文本"""
    if not at_users_text:
        return []
    
    at_users_list = at_users_text.strip().split('\n')
    at_users_list = [u.strip() for u in at_users_list if u.strip()]
    
    return at_users_list

def generate_single_content(client_id, task_id, batch_id, template, keywords_dict,
                           topics, at_users, location, publish_priority, current_user,
                           display_date=None, daily_order=0, client=None, image_editor_id=None, content_editor_id=None):
    """生成单篇文案"""
    # 替换模板中的标记
    title = template.title
    content = template.content

    # 替换关键词
    for mark, values in keywords_dict.items():
        if values:
            replacement = random.choice(values)
            pattern = r'\{' + re.escape(mark) + r'\}'
            title = re.sub(pattern, replacement, title)
            content = re.sub(pattern, replacement, content)

    # 计算展示时间
    display_time = None
    if display_date and client:
        from datetime import datetime, time, timedelta

        # 获取客户的展示时间设置
        start_time = client.display_start_time or time(9, 0)  # 默认9:00开始
        interval_min = client.interval_min or 30  # 默认最小间隔30分钟
        interval_max = client.interval_max or 120  # 默认最大间隔120分钟

        # 计算这篇文案的展示时间
        if daily_order == 0:
            display_time = start_time
        else:
            # 计算累计时间间隔
            total_minutes = 0
            for i in range(daily_order):
                interval = random.randint(interval_min, interval_max)
                total_minutes += interval

            # 基于开始时间计算展示时间
            start_datetime = datetime.combine(display_date, start_time)
            display_datetime = start_datetime + timedelta(minutes=total_minutes)
            display_time = display_datetime.time()

    # 检查生成内容的长度
    def calculate_title_length(text):
        """计算标题长度：字母数字算0.5个，emoji算1个"""
        length = 0
        for char in text:
            if ord(char) > 127:
                if '\u4e00' <= char <= '\u9fff':
                    length += 1  # 中文字符算1个
                else:
                    length += 1  # emoji算1个
            else:
                length += 0.5  # 字母数字算0.5个
        return int(length + 0.5)  # 四舍五入

    def calculate_content_length(text):
        """计算内容长度：中文算2个，其他算1个"""
        length = 0
        for char in text:
            if '\u4e00' <= char <= '\u9fff':
                length += 2  # 中文字符算2个
            else:
                length += 1  # 其他字符算1个
        return length

    # 检查标题和内容长度
    title_length = calculate_title_length(title)
    content_length = calculate_content_length(content)

    # 新生成的内容始终为草稿状态，让用户可以进一步编辑
    initial_workflow_status = 'draft'

    # 如果长度超出限制，设置特殊的内部审核状态标记
    if title_length > 20 or content_length > 1000:
        initial_internal_review_status = 'length_exceeded'
        print(f"内容长度超出限制 - 标题: {title_length}/20, 内容: {content_length}/1000, 状态设置为: {initial_workflow_status}, 内部状态: {initial_internal_review_status}")
    else:
        initial_internal_review_status = 'pending'
        print(f"内容长度符合要求 - 标题: {title_length}/20, 内容: {content_length}/1000, 状态设置为: {initial_workflow_status}, 内部状态: {initial_internal_review_status}")

    # 创建文案对象
    content_obj = Content(
        client_id=client_id,
        task_id=task_id,
        batch_id=batch_id,
        template_id=template.id,
        title=title,
        content=content,
        topics=json.dumps(topics) if topics else None,
        location=location,  # 位置信息直接存储为字符串
        display_date=display_date,  # 设置展示日期
        display_time=display_time,  # 设置展示时间
        publish_priority=publish_priority,
        workflow_status=initial_workflow_status,  # 始终为草稿状态
        internal_review_status=initial_internal_review_status,  # 设置内部审核状态
        created_by=current_user.id,
        image_editor_id=image_editor_id,
        content_editor_id=content_editor_id
    )

    # 设置@用户信息（通过ext_json存储）
    if at_users:
        content_obj.at_users_list = at_users
    
    return content_obj

def remove_duplicate_contents(contents):
    """移除重复内容"""
    unique_contents = []
    content_texts = set()
    
    for content in contents:
        # 使用标题和内容的组合作为唯一标识
        content_key = f"{content.title}|{content.content}"
        
        if content_key not in content_texts:
            content_texts.add(content_key)
            unique_contents.append(content)
    
    return unique_contents

def update_batch_content_count(batch_id):
    """更新批次的文案数量"""
    count = Content.query.filter_by(batch_id=batch_id).count()
    batch = Batch.query.get(batch_id)
    if batch:
        batch.content_count = count
        db.session.commit()

def process_topics_for_single_article(required_topics_list, random_topics_list, max_topics_count):
    """为单篇文章处理话题（每篇文章独立随机）

    逻辑：必选话题 + 随机话题 = 总数量(max_topics_count)
    如果必选话题数量 >= max_topics_count，则只使用必选话题的前max_topics_count个
    如果必选话题数量 < max_topics_count，则从随机话题中补充
    """
    # 必选话题直接包含
    article_topics = required_topics_list.copy()

    # 如果必选话题已经达到或超过最大数量，截取前max_topics_count个
    if len(article_topics) >= max_topics_count:
        return article_topics[:max_topics_count]

    # 计算需要从随机话题中选择的数量
    needed_random_count = max_topics_count - len(article_topics)

    # 从随机话题中随机选择需要的数量
    if random_topics_list and needed_random_count > 0:
        # 确保不超过可用的随机话题数量
        actual_random_count = min(needed_random_count, len(random_topics_list))
        selected_random_topics = random.sample(random_topics_list, actual_random_count)
        article_topics.extend(selected_random_topics)

    return article_topics

def process_at_users_for_single_article(at_users_list, random_at_users_count):
    """为单篇文章处理@用户（每篇文章独立随机）"""
    if not at_users_list or random_at_users_count <= 0:
        return []

    # 确保不超过可用的@用户数量
    actual_count = min(random_at_users_count, len(at_users_list))
    return random.sample(at_users_list, actual_count)

def process_location_for_single_article(location_list):
    """为单篇文章处理位置信息（每篇文章独立随机）"""
    print(f"process_location_for_single_article 输入: {location_list}")

    if not location_list:
        print("位置列表为空，返回None")
        return None

    # 随机选择一个位置
    selected_location = random.choice(location_list)
    print(f"随机选择的位置: {repr(selected_location)}")
    return selected_location